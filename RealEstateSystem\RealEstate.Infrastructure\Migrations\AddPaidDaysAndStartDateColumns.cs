using Microsoft.EntityFrameworkCore.Migrations;
using System;

#nullable disable

namespace RealEstate.Infrastructure.Migrations
{
    /// <summary>
    /// Migration to add PaidDays and StartDate columns to Properties table
    /// and handle existing NULL values
    /// </summary>
    public partial class AddPaidDaysAndStartDateColumns : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add PaidDays column with default value
            migrationBuilder.AddColumn<int>(
                name: "PaidDays",
                table: "Properties",
                type: "integer",
                nullable: false,
                defaultValue: 10);

            // Add StartDate column (nullable)
            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "StartDate",
                table: "Properties",
                type: "timestamp with time zone",
                nullable: true);

            // Update existing records with NULL PaidDays to default value
            migrationBuilder.Sql(@"
                UPDATE ""Properties"" 
                SET ""PaidDays"" = 10 
                WHERE ""PaidDays"" IS NULL;
            ");

            // For approved properties, set StartDate to CreatedAt as a reasonable default
            migrationBuilder.Sql(@"
                UPDATE ""Properties"" 
                SET ""StartDate"" = ""CreatedAt""
                WHERE ""Status"" = 2 AND ""StartDate"" IS NULL;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PaidDays",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "StartDate",
                table: "Properties");
        }
    }
}
